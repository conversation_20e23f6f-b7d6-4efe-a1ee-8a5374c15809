package com.ruoyi.fuguang.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 商家申请对象 merchant_application
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public class MerchantApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请ID */
    private Long applicationId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 营业执照图片地址 */
    @Excel(name = "营业执照")
    private String businessLicense;

    /** 店铺名称 */
    @Excel(name = "店铺名称")
    private String shopName;

    /** 店铺地址 */
    @Excel(name = "店铺地址")
    private String shopAddress;

    /** 店铺经度 */
    private String shopLongitude;

    /** 店铺纬度 */
    private String shopLatitude;

    /** 法人姓名 */
    @Excel(name = "法人姓名")
    private String legalPerson;

    /** 法人身份证号 */
    @Excel(name = "法人身份证号")
    private String legalIdCard;

    /** 法人联系方式 */
    @Excel(name = "法人联系方式")
    private String legalPhone;

    /** 经营范围 */
    @Excel(name = "经营范围")
    private String businessScope;

    /** 申请状态（0待审核 1审核通过 2审核拒绝） */
    @Excel(name = "申请状态", readConverterExp = "0=待审核,1=审核通过,2=审核拒绝")
    private String applicationStatus;

    /** 审核人 */
    @Excel(name = "审核人")
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核备注 */
    @Excel(name = "审核备注")
    private String auditRemark;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 用户昵称（关联查询用） */
    private String userNickName;

    /** 用户手机号（关联查询用） */
    private String userPhone;

    /** 申请ID数组（批量操作用） */
    private Long[] applicationIds;

    public void setApplicationId(Long applicationId)
    {
        this.applicationId = applicationId;
    }

    public Long getApplicationId() 
    {
        return applicationId;
    }

    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }

    public void setBusinessLicense(String businessLicense) 
    {
        this.businessLicense = businessLicense;
    }

    public String getBusinessLicense() 
    {
        return businessLicense;
    }

    public void setShopName(String shopName) 
    {
        this.shopName = shopName;
    }

    public String getShopName() 
    {
        return shopName;
    }

    public void setShopAddress(String shopAddress) 
    {
        this.shopAddress = shopAddress;
    }

    public String getShopAddress() 
    {
        return shopAddress;
    }

    public void setShopLongitude(String shopLongitude) 
    {
        this.shopLongitude = shopLongitude;
    }

    public String getShopLongitude() 
    {
        return shopLongitude;
    }

    public void setShopLatitude(String shopLatitude) 
    {
        this.shopLatitude = shopLatitude;
    }

    public String getShopLatitude() 
    {
        return shopLatitude;
    }

    public void setLegalPerson(String legalPerson) 
    {
        this.legalPerson = legalPerson;
    }

    public String getLegalPerson() 
    {
        return legalPerson;
    }

    public void setLegalIdCard(String legalIdCard) 
    {
        this.legalIdCard = legalIdCard;
    }

    public String getLegalIdCard() 
    {
        return legalIdCard;
    }

    public void setLegalPhone(String legalPhone) 
    {
        this.legalPhone = legalPhone;
    }

    public String getLegalPhone() 
    {
        return legalPhone;
    }

    public void setBusinessScope(String businessScope) 
    {
        this.businessScope = businessScope;
    }

    public String getBusinessScope() 
    {
        return businessScope;
    }

    public void setApplicationStatus(String applicationStatus) 
    {
        this.applicationStatus = applicationStatus;
    }

    public String getApplicationStatus() 
    {
        return applicationStatus;
    }

    public void setAuditBy(String auditBy) 
    {
        this.auditBy = auditBy;
    }

    public String getAuditBy() 
    {
        return auditBy;
    }

    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }

    public void setAuditRemark(String auditRemark) 
    {
        this.auditRemark = auditRemark;
    }

    public String getAuditRemark() 
    {
        return auditRemark;
    }

    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public void setUserNickName(String userNickName) 
    {
        this.userNickName = userNickName;
    }

    public String getUserNickName() 
    {
        return userNickName;
    }

    public void setUserPhone(String userPhone)
    {
        this.userPhone = userPhone;
    }

    public String getUserPhone()
    {
        return userPhone;
    }

    public void setApplicationIds(Long[] applicationIds)
    {
        this.applicationIds = applicationIds;
    }

    public Long[] getApplicationIds()
    {
        return applicationIds;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("applicationId", getApplicationId())
            .append("userId", getUserId())
            .append("businessLicense", getBusinessLicense())
            .append("shopName", getShopName())
            .append("shopAddress", getShopAddress())
            .append("shopLongitude", getShopLongitude())
            .append("shopLatitude", getShopLatitude())
            .append("legalPerson", getLegalPerson())
            .append("legalIdCard", getLegalIdCard())
            .append("legalPhone", getLegalPhone())
            .append("businessScope", getBusinessScope())
            .append("applicationStatus", getApplicationStatus())
            .append("auditBy", getAuditBy())
            .append("auditTime", getAuditTime())
            .append("auditRemark", getAuditRemark())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("userNickName", getUserNickName())
            .append("userPhone", getUserPhone())
            .toString();
    }
}
