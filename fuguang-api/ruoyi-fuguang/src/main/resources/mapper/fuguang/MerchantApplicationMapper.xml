<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.fuguang.mapper.MerchantApplicationMapper">
    
    <resultMap type="MerchantApplication" id="MerchantApplicationResult">
        <result property="applicationId"    column="application_id"    />
        <result property="userId"    column="user_id"    />
        <result property="businessLicense"    column="business_license"    />
        <result property="shopName"    column="shop_name"    />
        <result property="shopAddress"    column="shop_address"    />
        <result property="shopLongitude"    column="shop_longitude"    />
        <result property="shopLatitude"    column="shop_latitude"    />
        <result property="legalPerson"    column="legal_person"    />
        <result property="legalIdCard"    column="legal_id_card"    />
        <result property="legalPhone"    column="legal_phone"    />
        <result property="businessScope"    column="business_scope"    />
        <result property="applicationStatus"    column="application_status"    />
        <result property="auditBy"    column="audit_by"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="auditRemark"    column="audit_remark"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="userNickName"    column="user_nick_name"    />
        <result property="userPhone"    column="user_phone"    />
    </resultMap>

    <sql id="selectMerchantApplicationVo">
        select ma.application_id, ma.user_id, ma.business_license, ma.shop_name, ma.shop_address, 
               ma.shop_longitude, ma.shop_latitude, ma.legal_person, ma.legal_id_card, ma.legal_phone, 
               ma.business_scope, ma.application_status, ma.audit_by, ma.audit_time, ma.audit_remark, 
               ma.status, ma.del_flag, ma.create_by, ma.create_time, ma.update_by, ma.update_time, ma.remark,
               au.nick_name as user_nick_name, au.phonenumber as user_phone
        from merchant_application ma
        left join app_user au on ma.user_id = au.user_id
    </sql>

    <select id="selectMerchantApplicationList" parameterType="MerchantApplication" resultMap="MerchantApplicationResult">
        <include refid="selectMerchantApplicationVo"/>
        <where>  
            <if test="userId != null "> and ma.user_id = #{userId}</if>
            <if test="shopName != null  and shopName != ''"> and ma.shop_name like concat('%', #{shopName}, '%')</if>
            <if test="legalPerson != null  and legalPerson != ''"> and ma.legal_person like concat('%', #{legalPerson}, '%')</if>
            <if test="applicationStatus != null  and applicationStatus != ''"> and ma.application_status = #{applicationStatus}</if>
            <if test="auditBy != null  and auditBy != ''"> and ma.audit_by like concat('%', #{auditBy}, '%')</if>
            <if test="status != null  and status != ''"> and ma.status = #{status}</if>
            <if test="userNickName != null  and userNickName != ''"> and au.nick_name like concat('%', #{userNickName}, '%')</if>
            <if test="userPhone != null  and userPhone != ''"> and au.phonenumber like concat('%', #{userPhone}, '%')</if>
            and ma.del_flag = '0'
        </where>
        order by ma.create_time desc
    </select>
    
    <select id="selectMerchantApplicationByApplicationId" parameterType="Long" resultMap="MerchantApplicationResult">
        <include refid="selectMerchantApplicationVo"/>
        where ma.application_id = #{applicationId} and ma.del_flag = '0'
    </select>

    <select id="selectMerchantApplicationByUserId" parameterType="Long" resultMap="MerchantApplicationResult">
        <include refid="selectMerchantApplicationVo"/>
        where ma.user_id = #{userId} and ma.del_flag = '0'
        order by ma.create_time desc
        limit 1
    </select>

    <select id="checkUserApplicationExists" parameterType="Long" resultType="int">
        select count(1) from merchant_application 
        where user_id = #{userId} and application_status in ('0', '1') and del_flag = '0'
    </select>
        
    <insert id="insertMerchantApplication" parameterType="MerchantApplication" useGeneratedKeys="true" keyProperty="applicationId">
        insert into merchant_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="businessLicense != null and businessLicense != ''">business_license,</if>
            <if test="shopName != null and shopName != ''">shop_name,</if>
            <if test="shopAddress != null and shopAddress != ''">shop_address,</if>
            <if test="shopLongitude != null">shop_longitude,</if>
            <if test="shopLatitude != null">shop_latitude,</if>
            <if test="legalPerson != null and legalPerson != ''">legal_person,</if>
            <if test="legalIdCard != null and legalIdCard != ''">legal_id_card,</if>
            <if test="legalPhone != null and legalPhone != ''">legal_phone,</if>
            <if test="businessScope != null">business_scope,</if>
            <if test="applicationStatus != null">application_status,</if>
            <if test="auditBy != null">audit_by,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="auditRemark != null">audit_remark,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="businessLicense != null and businessLicense != ''">#{businessLicense},</if>
            <if test="shopName != null and shopName != ''">#{shopName},</if>
            <if test="shopAddress != null and shopAddress != ''">#{shopAddress},</if>
            <if test="shopLongitude != null">#{shopLongitude},</if>
            <if test="shopLatitude != null">#{shopLatitude},</if>
            <if test="legalPerson != null and legalPerson != ''">#{legalPerson},</if>
            <if test="legalIdCard != null and legalIdCard != ''">#{legalIdCard},</if>
            <if test="legalPhone != null and legalPhone != ''">#{legalPhone},</if>
            <if test="businessScope != null">#{businessScope},</if>
            <if test="applicationStatus != null">#{applicationStatus},</if>
            <if test="auditBy != null">#{auditBy},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="auditRemark != null">#{auditRemark},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateMerchantApplication" parameterType="MerchantApplication">
        update merchant_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="businessLicense != null and businessLicense != ''">business_license = #{businessLicense},</if>
            <if test="shopName != null and shopName != ''">shop_name = #{shopName},</if>
            <if test="shopAddress != null and shopAddress != ''">shop_address = #{shopAddress},</if>
            <if test="shopLongitude != null">shop_longitude = #{shopLongitude},</if>
            <if test="shopLatitude != null">shop_latitude = #{shopLatitude},</if>
            <if test="legalPerson != null and legalPerson != ''">legal_person = #{legalPerson},</if>
            <if test="legalIdCard != null and legalIdCard != ''">legal_id_card = #{legalIdCard},</if>
            <if test="legalPhone != null and legalPhone != ''">legal_phone = #{legalPhone},</if>
            <if test="businessScope != null">business_scope = #{businessScope},</if>
            <if test="applicationStatus != null">application_status = #{applicationStatus},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where application_id = #{applicationId}
    </update>

    <delete id="deleteMerchantApplicationByApplicationId" parameterType="Long">
        update merchant_application set del_flag = '2' where application_id = #{applicationId}
    </delete>

    <delete id="deleteMerchantApplicationByApplicationIds" parameterType="String">
        update merchant_application set del_flag = '2' where application_id in 
        <foreach item="applicationId" collection="array" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
    </delete>

</mapper>
